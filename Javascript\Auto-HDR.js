// ==UserScript==
// @name         Auto HDR Pro
// @namespace    http://taeparlaytampermonkey.net/
// @version      2.0
// @description  Intelligently applies HDR effects to videos and images, now with Shadow DOM support and a modern class-based structure for optimal performance.
// <AUTHOR> (Refactored by <PERSON>)
// @icon         https://www.google.com/s2/favicons?sz=64&domain=microsoft.com
// @match        *://*/*
// @grant        GM_getValue
// @grant        GM_setValue
// @grant        GM_addStyle
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';

    /**
     * The main class for handling HDR effects on media elements.
     * Encapsulates all functionality including settings, UI, and media processing.
     */
    class AutoHDR {
        // Constants
        SCRIPT_NAME = 'AutoHDRSettings';
        MEDIA_SELECTOR = 'video, img';

        // State
        settings = {};
        processedElements = new WeakSet();
        observer = null;
        cssFilterString = '';

        // Default settings
        DEFAULT_SETTINGS = {
            enabled: true,
            brightness: 0.95,
            contrast: 1.05,
            saturation: 1.10,
            excludedSites: ['example.com'],
            maxCanvasDimension: 1500,
            enableGUI: true
        };

        // Constants for IDs and classes to avoid magic strings
        CONSTANTS = {
            HOVER_AREA_ID: 'autohdr-hover-area',
            BUTTON_ID: 'autohdr-settings-button',
            PANEL_ID: 'autohdr-settings-panel',
            PANEL_SHOW_CLASS: 'show',
            SAVE_BUTTON_ID: 'save',
            ENABLED_CHECKBOX_ID: 'enabled',
            BRIGHTNESS_INPUT_ID: 'brightness',
            CONTRAST_INPUT_ID: 'contrast',
            SATURATION_INPUT_ID: 'saturation',
            EXCLUDED_SITES_TEXTAREA_ID: 'excludedSites'
        };

        constructor() {
            // Eagerly load settings
            this.loadSettings();
        }

        /**
         * Initializes the script, sets up observers and the GUI.
         */
        init() {
            if (this.isSiteExcluded()) {
                console.log('AutoHDR: Site is excluded, script will not run.');
                return;
            }

            if (this.settings.enableGUI) {
                // Defer GUI creation until the body is ready
                if (document.body) {
                    this.createSettingsGUI();
                } else {
                    document.addEventListener('DOMContentLoaded', () => this.createSettingsGUI(), { once: true });
                }
            }

            this.updateCssFilterString();
            this.startObserver();
        }

        /**
         * Loads settings from storage or uses defaults.
         */
        loadSettings() {
            try {
                const saved = GM_getValue(this.SCRIPT_NAME, null);
                this.settings = saved ? { ...this.DEFAULT_SETTINGS, ...JSON.parse(saved) } : { ...this.DEFAULT_SETTINGS };
                this.validateSettings();
            } catch (e) {
                console.error('AutoHDR: Error loading settings, falling back to defaults.', e);
                this.settings = { ...this.DEFAULT_SETTINGS };
            }
        }

        /**
         * Validates and sanitizes settings to ensure they are within reasonable bounds.
         */
        validateSettings() {
            const fields = ['brightness', 'contrast', 'saturation'];
            fields.forEach(field => {
                const val = parseFloat(this.settings[field]);
                this.settings[field] = isNaN(val) ? this.DEFAULT_SETTINGS[field] : Math.max(0.1, Math.min(3, val));
            });
            this.settings.maxCanvasDimension = Math.max(500, Math.min(3000, parseInt(this.settings.maxCanvasDimension, 10) || this.DEFAULT_SETTINGS.maxCanvasDimension));
            this.settings.enabled = Boolean(this.settings.enabled);
            this.settings.enableGUI = Boolean(this.settings.enableGUI);
            this.settings.excludedSites = Array.isArray(this.settings.excludedSites) ? this.settings.excludedSites : [];
        }

        /**
         * Saves the current settings to storage.
         */
        saveSettings() {
            GM_setValue(this.SCRIPT_NAME, JSON.stringify(this.settings));
        }

        /**
         * Checks if the current site is in the exclusion list.
         * @returns {boolean}
         */
        isSiteExcluded() {
            return this.settings.excludedSites.some(site => site && window.location.hostname.includes(site.trim()));
        }

        /**
         * Updates the master CSS filter string based on current settings.
         */
        updateCssFilterString() {
            this.cssFilterString = `brightness(${this.settings.brightness}) contrast(${this.settings.contrast}) saturate(${this.settings.saturation})`;
        }

        /**
         * Applies the HDR effect to a single media element.
         * @param {HTMLVideoElement|HTMLImageElement} element
         */
        applyEffect(element) {
            if (this.processedElements.has(element) || this.isSiteExcluded()) {
                return;
            }

            // Always revert first to handle potential style changes
            this.revertEffect(element);

            if (!this.settings.enabled) {
                return;
            }

            if (element.tagName === 'VIDEO') {
                element.style.filter = this.cssFilterString;
                element.dataset.hdrApplied = 'video';
            } else if (element.tagName === 'IMG') {
                if (element.complete) {
                    this.processImage(element);
                } else {
                    element.addEventListener('load', () => this.processImage(element), { once: true });
                }
            }
            this.processedElements.add(element);
        }

        /**
         * Reverts the HDR effect from a single element.
         * @param {HTMLElement} el
         */
        revertEffect(el) {
            if (!el.dataset?.hdrApplied) return;

            el.style.filter = '';
            if (el.dataset.hdrApplied.includes('canvas') && el.dataset.originalSrc) {
                el.src = el.dataset.originalSrc;
            }

            delete el.dataset.hdrApplied;
            delete el.dataset.originalSrc;
            this.processedElements.delete(el);
        }

        /**
         * Decides whether to use CSS filters or Canvas processing for an image.
         * @param {HTMLImageElement} img
         */
        processImage(img) {
            if (!img.naturalWidth) return; // Skip empty/broken images

            const isCrossOrigin = () => {
                try {
                    return !img.src.startsWith('data:') && new URL(img.src, window.location.href).origin !== window.location.origin;
                } catch {
                    return true;
                }
            };

            if (img.naturalWidth > this.settings.maxCanvasDimension || img.naturalHeight > this.settings.maxCanvasDimension || isCrossOrigin()) {
                img.style.filter = this.cssFilterString;
                img.dataset.hdrApplied = 'css';
            } else {
                this.processImageWithCanvas(img);
            }
        }

        /**
         * Applies HDR effect to an image using the Canvas API for higher fidelity.
         * @param {HTMLImageElement} img
         */
        processImageWithCanvas(img) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = img.naturalWidth;
            canvas.height = img.naturalHeight;

            try {
                ctx.drawImage(img, 0, 0);
                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                const data = imageData.data;
                const { brightness, contrast, saturation } = this.settings;

                for (let i = 0; i < data.length; i += 4) {
                    let r = data[i], g = data[i + 1], b = data[i + 2];

                    // Combined brightness and contrast
                    r = ((r - 128) * contrast + 128) * brightness;
                    g = ((g - 128) * contrast + 128) * brightness;
                    b = ((b - 128) * contrast + 128) * brightness;

                    // Saturation
                    const gray = 0.299 * r + 0.587 * g + 0.114 * b;
                    r = gray + (r - gray) * saturation;
                    g = gray + (g - gray) * saturation;
                    b = gray + (b - gray) * saturation;

                    // Clamp values
                    data[i] = Math.max(0, Math.min(255, r));
                    data[i + 1] = Math.max(0, Math.min(255, g));
                    data[i + 2] = Math.max(0, Math.min(255, b));
                }

                ctx.putImageData(imageData, 0, 0);
                if (!img.src.startsWith('data:')) {
                    img.dataset.originalSrc = img.src;
                }
                img.src = canvas.toDataURL();
                img.dataset.hdrApplied = 'canvas';
            } catch (e) {
                // Fallback to CSS filter on canvas error
                img.style.filter = this.cssFilterString;
                img.dataset.hdrApplied = 'css-fallback';
                console.error('AutoHDR: Canvas processing failed, falling back to CSS filter.', e);
            }
        }

        /**
         * Processes all media within a given root (document or shadowRoot).
         * @param {Document|ShadowRoot} root
         */
        processAllMedia(root) {
            root.querySelectorAll(this.MEDIA_SELECTOR).forEach(el => this.applyEffect(el));
        }

        /**
         * Re-applies effects to all known media elements across the document.
         */
        refreshAllEffects() {
            this.updateCssFilterString();
            // Clear the processed set to force re-evaluation for all elements
            this.processedElements = new WeakSet();
            this.processAllMedia(document);
            document.querySelectorAll('*').forEach(el => {
                if (el.shadowRoot) {
                    this.processAllMedia(el.shadowRoot);
                }
            });
        }

        /**
         * Starts the MutationObserver to watch for new media elements.
         */
        startObserver() {
            this.observer?.disconnect();

            this.observer = new MutationObserver(mutations => {
                for (const mutation of mutations) {
                    if (mutation.type !== 'childList') continue;
                    for (const node of mutation.addedNodes) {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            this.handleAddedNode(node);
                        }
                    }
                }
            });

            this.observeNode(document.documentElement);
        }

        /**
         * Handles a newly added node, applying effects and checking for shadow DOM.
         * @param {Element} node
         */
        handleAddedNode(node) {
            // If the node itself is a media element
            if (node.matches(this.MEDIA_SELECTOR)) {
                this.applyEffect(node);
            }
            // If the node contains media elements
            node.querySelectorAll(this.MEDIA_SELECTOR).forEach(el => this.applyEffect(el));
            // If the node has a shadow root, observe it
            if (node.shadowRoot) {
                this.observeNode(node.shadowRoot);
            }
            // Also check all descendants for shadow roots
            node.querySelectorAll('*').forEach(el => {
                if (el.shadowRoot) {
                    this.observeNode(el.shadowRoot);
                }
            });
        }

        /**
         * Observes a specific node (document or shadow root).
         * @param {Document|ShadowRoot|Element} node
         */
        observeNode(node) {
            this.processAllMedia(node); // Process existing media first
            this.observer.observe(node, { childList: true, subtree: true });
        }

        /**
         * Creates and injects the settings GUI into the page.
         */
        createSettingsGUI() {
            if (document.getElementById(this.CONSTANTS.BUTTON_ID)) return;

            GM_addStyle(`
                #${this.CONSTANTS.HOVER_AREA_ID} { position: fixed; top: 0; right: 0; width: 80px; height: 80px; z-index: 9998; }
                #${this.CONSTANTS.BUTTON_ID} {
                    position: fixed; top: 20px; right: -60px; z-index: 9999; background: linear-gradient(135deg, #3b82f6, #1d4ed8);
                    color: white; border: none; padding: 12px 16px; border-radius: 8px 0 0 8px; cursor: pointer;
                    font-weight: bold; box-shadow: 0 4px 12px rgba(59,130,246,0.3); transition: all 0.3s ease; opacity: 0.8;
                }
                #${this.CONSTANTS.HOVER_AREA_ID}:hover #${this.CONSTANTS.BUTTON_ID} { right: 0; opacity: 1; }
                #${this.CONSTANTS.PANEL_ID} {
                    position: fixed; top: 70px; right: -100%; z-index: 9997; background: rgba(15,15,15,0.95);
                    color: white; padding: 24px; border-radius: 12px 0 0 12px; width: 320px; backdrop-filter: blur(10px);
                    box-shadow: 0 8px 32px rgba(0,0,0,0.6); border: 1px solid rgba(255,255,255,0.1);
                    transition: right 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
                }
                #${this.CONSTANTS.PANEL_ID}.${this.CONSTANTS.PANEL_SHOW_CLASS} { right: 0; }
                #${this.CONSTANTS.PANEL_ID} h3 { margin: 0 0 16px 0; color: #3b82f6; font-size: 18px; }
                #${this.CONSTANTS.PANEL_ID} label { display: flex; align-items: center; justify-content: space-between; margin: 12px 0; font-size: 14px; }
                #${this.CONSTANTS.PANEL_ID} input[type="number"] { width: 80px; padding: 6px 8px; border-radius: 6px; border: 1px solid #444; background: #2a2a2a; color: white; }
                #${this.CONSTANTS.PANEL_ID} input[type="checkbox"] { margin-right: 8px; transform: scale(1.2); }
                #${this.CONSTANTS.PANEL_ID} textarea { width: 100%; height: 60px; margin-top: 8px; padding: 8px; border-radius: 6px; border: 1px solid #444; background: #2a2a2a; color: white; resize: vertical; box-sizing: border-box; }
                #${this.CONSTANTS.PANEL_ID} .setting-row { justify-content: flex-start; }
                #${this.CONSTANTS.PANEL_ID} button#${this.CONSTANTS.SAVE_BUTTON_ID} { background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; margin-top: 16px; width: 100%; font-weight: bold; transition: all 0.2s ease; }
                #${this.CONSTANTS.PANEL_ID} button#${this.CONSTANTS.SAVE_BUTTON_ID}:hover { background: linear-gradient(135deg, #2563eb, #1e40af); }
            `);

            const hoverArea = document.createElement('div');
            hoverArea.id = this.CONSTANTS.HOVER_AREA_ID;

            const button = document.createElement('button');
            button.id = this.CONSTANTS.BUTTON_ID;
            button.textContent = 'HDR';

            const panel = document.createElement('div');
            panel.id = this.CONSTANTS.PANEL_ID;

            // --- CSP-Safe DOM Creation ---
            const title = document.createElement('h3');
            title.textContent = 'HDR Settings';
            panel.appendChild(title);

            const createSetting = (id, type, text, props = {}) => {
                const label = document.createElement('label');
                const input = document.createElement(type === 'textarea' ? 'textarea' : 'input');
                input.id = id;

                if (type !== 'textarea') {
                    input.type = type;
                }

                Object.assign(input, props);

                if (type === 'checkbox') {
                    label.className = 'setting-row';
                    label.appendChild(input);
                    label.append(` ${text}`);
                } else if (type === 'textarea') {
                    label.style.flexDirection = 'column';
                    label.style.alignItems = 'flex-start';
                    label.textContent = text;
                    label.appendChild(input);
                } else {
                    label.textContent = `${text}: `;
                    label.appendChild(input);
                }
                return label;
            };

            panel.appendChild(createSetting(this.CONSTANTS.ENABLED_CHECKBOX_ID, 'checkbox', 'Enable HDR'));
            panel.appendChild(createSetting(this.CONSTANTS.BRIGHTNESS_INPUT_ID, 'number', 'Brightness', { step: 0.01, min: 0.1, max: 3 }));
            panel.appendChild(createSetting(this.CONSTANTS.CONTRAST_INPUT_ID, 'number', 'Contrast', { step: 0.01, min: 0.1, max: 3 }));
            panel.appendChild(createSetting(this.CONSTANTS.SATURATION_INPUT_ID, 'number', 'Saturation', { step: 0.01, min: 0.1, max: 3 }));
            panel.appendChild(createSetting(this.CONSTANTS.EXCLUDED_SITES_TEXTAREA_ID, 'textarea', 'Excluded Sites (comma-separated):', { placeholder: 'example.com, another.org' }));

            const saveButton = document.createElement('button');
            saveButton.id = this.CONSTANTS.SAVE_BUTTON_ID;
            saveButton.textContent = 'Save & Apply';
            panel.appendChild(saveButton);
            // --- End CSP-Safe DOM Creation ---

            hoverArea.appendChild(button);
            document.body.append(hoverArea, panel);

            const updatePanelInputs = () => {
                panel.querySelector(`#${this.CONSTANTS.ENABLED_CHECKBOX_ID}`).checked = this.settings.enabled;
                panel.querySelector(`#${this.CONSTANTS.BRIGHTNESS_INPUT_ID}`).value = this.settings.brightness;
                panel.querySelector(`#${this.CONSTANTS.CONTRAST_INPUT_ID}`).value = this.settings.contrast;
                panel.querySelector(`#${this.CONSTANTS.SATURATION_INPUT_ID}`).value = this.settings.saturation;
                panel.querySelector(`#${this.CONSTANTS.EXCLUDED_SITES_TEXTAREA_ID}`).value = this.settings.excludedSites.join(', ');
            };

            button.addEventListener('click', (e) => {
                e.stopPropagation();
                const isVisible = panel.classList.toggle(this.CONSTANTS.PANEL_SHOW_CLASS);
                if (isVisible) {
                    updatePanelInputs();
                }
            });

            document.addEventListener('click', (e) => {
                if (!panel.contains(e.target) && !button.contains(e.target)) {
                    panel.classList.remove(this.CONSTANTS.PANEL_SHOW_CLASS);
                }
            });

            saveButton.addEventListener('click', () => {
                this.settings.enabled = panel.querySelector(`#${this.CONSTANTS.ENABLED_CHECKBOX_ID}`).checked;
                this.settings.brightness = parseFloat(panel.querySelector(`#${this.CONSTANTS.BRIGHTNESS_INPUT_ID}`).value);
                this.settings.contrast = parseFloat(panel.querySelector(`#${this.CONSTANTS.CONTRAST_INPUT_ID}`).value);
                this.settings.saturation = parseFloat(panel.querySelector(`#${this.CONSTANTS.SATURATION_INPUT_ID}`).value);
                this.settings.excludedSites = panel.querySelector(`#${this.CONSTANTS.EXCLUDED_SITES_TEXTAREA_ID}`).value.split(',').map(s => s.trim()).filter(Boolean);

                this.validateSettings();
                this.saveSettings();
                this.refreshAllEffects();

                panel.classList.remove(this.CONSTANTS.PANEL_SHOW_CLASS);

                if (this.isSiteExcluded()) {
                    alert('AutoHDR: This site is now excluded. The page will reload to disable the script.');
                    window.location.reload();
                }
            });
        }
    }

    // Initialize the script once the DOM is ready
    const autoHDR = new AutoHDR();
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => autoHDR.init(), { once: true });
    } else {
        autoHDR.init();
    }

})();