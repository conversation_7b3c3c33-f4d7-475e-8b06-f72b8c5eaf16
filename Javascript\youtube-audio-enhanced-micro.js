// ==UserScript==
// @name         YouTube Audio HD - Micro
// @namespace    https://tampermonkey.com/
// @version      6.4
// @description  Ultra-minimal HD audio enhancement with robust GUI logic
// @icon         https://www.google.com/s2/favicons?sz=64&domain=youtube.com
// <AUTHOR> (dengan perbaikan besar oleh AI)
// @match        https://www.youtube.com/*
// @match        https://music.youtube.com/*
// @match        https://m.youtube.com/*
// @license      MIT
// @grant        GM_getValue
// @grant        GM_setValue
// ==/UserScript==

(function() {
  'use strict';

  class MicroAudioProcessor {
    constructor() {
      this.ctx = null;
      this.nodes = {};
      this.cache = { video: null, indicator: null, dialog: null };
      this.debounceTimers = {};
      this.autoCloseTimer = null;
      this.isInitialized = false;
      this.settings = {
        gain: GM_getValue('gain', 1.2),
        bass: GM_getValue('bass', 1.8),
        treble: GM_getValue('treble', 1.3),
        presence: GM_getValue('presence', 1.25)
      };

      this.styles = {
        indicator: `position:fixed;bottom:20px;right:20px;padding:12px;background:linear-gradient(135deg,#667eea,#764ba2);color:white;border-radius:50%;font-size:16px;cursor:pointer;z-index:9999;opacity:0;transition:all 0.3s cubic-bezier(0.4,0,0.2,1);box-shadow:0 4px 12px rgba(102,126,234,0.3);transform:scale(0.8);`,
        indicatorVisible: `opacity:0.9;transform:scale(1);`,
        dialog: `position:fixed;top:50%;left:50%;transform:translate(-50%,-50%) scale(0.9);background:linear-gradient(135deg,#2d2d2d,#3a3a3a);color:white;padding:24px;border-radius:12px;z-index:10000;min-width:320px;box-shadow:0 20px 40px rgba(0,0,0,0.3);opacity:0;transition:all 0.3s cubic-bezier(0.4,0,0.2,1);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,0.1);`,
        dialogVisible: `opacity:1;transform:translate(-50%,-50%) scale(1);`
      };
    }

    async init() {
      if (this.isInitialized) return;

      const video = document.querySelector('video.html5-main-video, video[src]');
      if (!video) {
        console.error('🎵 Inisialisasi gagal: Video tidak ditemukan.');
        return;
      }
      // Jangan re-inisialisasi untuk video yang sama
      if (video === this.cache.video) return;
      this.cache.video = video;

      try {
        const AudioContext = window.AudioContext || window.webkitAudioContext;
        this.ctx = new AudioContext();
        if (this.ctx.state === 'suspended') await this.ctx.resume();

        const source = this.ctx.createMediaElementSource(video);
        this.nodes = {
          gain: this.ctx.createGain(),
          bass: this.createFilter('peaking', 80, 1.5),
          treble: this.createFilter('highshelf', 8000, 0.7),
          presence: this.createFilter('peaking', 2800, 1.2)
        };

        source
          .connect(this.nodes.gain)
          .connect(this.nodes.bass)
          .connect(this.nodes.treble)
          .connect(this.nodes.presence)
          .connect(this.ctx.destination);

        this.updateSettings();
        this.createIndicator();
        this.isInitialized = true;
        console.log('🎵 Audio enhancer berhasil diinisialisasi.');
      } catch (e) {
        // Jika sumber media sudah digunakan, jangan tampilkan error sebagai kegagalan
        if (e.name === 'InvalidStateError') {
          console.warn('🎵 Audio context sudah terhubung. Mengabaikan inisialisasi ulang.');
          this.isInitialized = true; // Tandai sebagai sudah diinisialisasi agar tidak dicoba lagi
        } else {
          console.error('🎵 Terjadi kesalahan saat inisialisasi audio:', e);
          this.isInitialized = false;
        }
      }
    }

    // PERBAIKAN: Fungsi baru untuk membersihkan GUI saat video tidak ada
    cleanup() {
      if (!this.isInitialized) return;
      console.log('🎵 Membersihkan audio enhancer...');
      this.closeDialog();
      if (this.cache.indicator && this.cache.indicator.parentNode) {
        this.cache.indicator.remove();
      }
      // Reset semua cache dan state
      this.isInitialized = false;
      this.cache = { video: null, indicator: null, dialog: null };
      if (this.ctx && this.ctx.state !== 'closed') {
        this.ctx.close();
      }
      this.ctx = null;
    }

    createFilter(type, freq, q) {
      const filter = this.ctx.createBiquadFilter();
      filter.type = type;
      filter.frequency.value = freq;
      filter.Q.value = q;
      return filter;
    }

    updateSettings() {
      if (!this.nodes.gain) return;
      requestAnimationFrame(() => {
        this.nodes.gain.gain.value = this.settings.gain;
        this.nodes.bass.gain.value = (this.settings.bass - 1) * 12;
        this.nodes.treble.gain.value = (this.settings.treble - 1) * 8;
        this.nodes.presence.gain.value = (this.settings.presence - 1) * 6;
      });
    }

    saveSettings() {
      clearTimeout(this.debounceTimers.save);
      this.debounceTimers.save = setTimeout(() => {
        Object.entries(this.settings).forEach(([k, v]) => GM_setValue(k, v));
      }, 300);
    }

    createIndicator() {
      if (document.querySelector('.audio-enhancer-indicator')) return;

      const indicator = document.createElement('div');
      indicator.innerHTML = '🎵';
      indicator.style.cssText = this.styles.indicator;
      indicator.className = 'audio-enhancer-indicator';

      indicator.addEventListener('click', () => this.showControls(), { passive: true });

      this.cache.indicator = indicator;
      document.body.appendChild(indicator);

      requestAnimationFrame(() => {
        indicator.style.cssText += this.styles.indicatorVisible;
      });
    }

    showControls() {
      if (document.querySelector('.audio-enhancer-dialog')) return;

      const dialog = this.createDialog();
      this.cache.dialog = dialog;
      document.body.appendChild(dialog);

      requestAnimationFrame(() => {
        dialog.style.cssText += this.styles.dialogVisible;
      });
      this.resetAutoCloseTimer();
    }

    resetAutoCloseTimer() {
        clearTimeout(this.autoCloseTimer);
        this.autoCloseTimer = setTimeout(() => this.closeDialog(), 30000);
    }

    createDialog() {
      const dialog = document.createElement('div');
      dialog.style.cssText = this.styles.dialog;
      dialog.className = 'audio-enhancer-dialog';
      dialog.innerHTML = `
        <h3 style="margin:0 0 20px 0;font-size:18px;font-weight:600;">🎛️ Audio Controls</h3>
        ${this.createSlider('gain', 'Volume', 0.5, 2.5)}
        ${this.createSlider('bass', 'Bass', 0.5, 3)}
        ${this.createSlider('treble', 'Treble', 0.5, 3)}
        ${this.createSlider('presence', 'Presence', 0.5, 3)}
        <div style="margin-top:20px;display:flex;gap:8px;flex-wrap:wrap;">
          <button class="preset-btn" data-preset="bass">🔊 Bass</button>
          <button class="preset-btn" data-preset="clear">✨ Clear</button>
          <button class="preset-btn" data-preset="balanced">⚖️ Balanced</button>
        </div>
        <button id="close-dialog" style="position:absolute;top:10px;right:10px;padding:5px;background:transparent;color:white;border:none;font-size:20px;cursor:pointer;line-height:1;">×</button>
      `;
      this.setupDialogEvents(dialog);
      return dialog;
    }

    setupDialogEvents(dialog) {
      dialog.addEventListener('input', (e) => {
        if (e.target.type === 'range') {
          const slider = e.target;
          const val = parseFloat(slider.value);
          this.settings[slider.id] = val;
          dialog.querySelector(`#${slider.id}-val`).textContent = val.toFixed(1);
          this.resetAutoCloseTimer();
          clearTimeout(this.debounceTimers[slider.id]);
          this.debounceTimers[slider.id] = setTimeout(() => {
            this.updateSettings();
            this.saveSettings();
          }, 50);
        }
      }, { passive: true });

      dialog.addEventListener('click', (e) => {
        if (e.target.classList.contains('preset-btn')) {
          this.resetAutoCloseTimer();
          this.applyPreset(e.target.dataset.preset);
        } else if (e.target.id === 'close-dialog') {
          this.closeDialog();
        }
      }, { passive: true });
    }

    closeDialog() {
      clearTimeout(this.autoCloseTimer);
      if (this.cache.dialog) {
        this.cache.dialog.style.opacity = '0';
        this.cache.dialog.style.transform = 'translate(-50%, -50%) scale(0.9)';
        setTimeout(() => {
          if (this.cache.dialog && this.cache.dialog.parentNode) {
            this.cache.dialog.remove();
          }
          this.cache.dialog = null;
        }, 300);
      }
    }

    createSlider(id, label, min, max) {
      const val = this.settings[id];
      return `
        <div style="margin:12px 0;display:flex;justify-content:space-between;align-items:center;">
          <label for="${id}" style="font-weight:500;min-width:70px;">${label}:</label>
          <input type="range" id="${id}" name="${id}" min="${min}" max="${max}" step="0.1" value="${val}"
                 style="flex-grow:1;margin:0 12px;accent-color:#667eea;">
          <span id="${id}-val" style="min-width:35px;text-align:right;font-family:monospace;">${val.toFixed(1)}</span>
        </div>
      `;
    }

    applyPreset(type) {
      const presets = {
        bass: { gain: 1.3, bass: 2.5, treble: 1.1, presence: 1.0 },
        clear: { gain: 1.2, bass: 1.2, treble: 1.8, presence: 1.6 },
        balanced: { gain: 1.15, bass: 1.5, treble: 1.3, presence: 1.25 }
      };
      if (presets[type]) {
        Object.assign(this.settings, presets[type]);
        this.updateSettings();
        this.saveSettings();
        this.updateDialogSliders();
      }
    }

    updateDialogSliders() {
      if (!this.cache.dialog) return;
      Object.entries(this.settings).forEach(([key, value]) => {
        const slider = this.cache.dialog.querySelector(`#${key}`);
        const valueDisplay = this.cache.dialog.querySelector(`#${key}-val`);
        if (slider) slider.value = value;
        if (valueDisplay) valueDisplay.textContent = value.toFixed(1);
      });
    }

    injectStyles() {
      if (document.querySelector('#audio-enhancer-styles')) return;
      const style = document.createElement('style');
      style.id = 'audio-enhancer-styles';
      style.textContent = `
        .preset-btn {
          margin:2px;padding:10px 14px;background:#667eea;color:white;
          border:none;border-radius:6px;cursor:pointer;font-size:13px;
          transition:all 0.2s cubic-bezier(0.4,0,0.2,1);
          box-shadow:0 2px 4px rgba(102,126,234,0.2);
        }
        .preset-btn:hover {
          background:#5a6fd8;transform:translateY(-1px);
          box-shadow:0 4px 8px rgba(102,126,234,0.3);
        }
        .preset-btn:active { transform:translateY(0); }
      `;
      document.head.appendChild(style);
    }
  }

  // =========================================================================
  // PERBAIKAN BESAR: Logika inisialisasi dan deteksi navigasi ditulis ulang sepenuhnya
  // untuk keandalan maksimum di YouTube.
  // =========================================================================

  const audioProcessor = new MicroAudioProcessor();
  audioProcessor.injectStyles();

  let checkTimeout;

  // Fungsi pusat yang memeriksa state halaman
  const runCheck = () => {
    // Cari elemen video utama. Ini adalah cara paling andal.
    const videoElement = document.querySelector('video.html5-main-video, video[src]');

    if (videoElement && !audioProcessor.isInitialized) {
      // Jika video ada DAN prosesor belum aktif, jalankan.
      console.log('🎵 Video terdeteksi. Menjalankan audio enhancer.');
      audioProcessor.init();
    } else if (!videoElement && audioProcessor.isInitialized) {
      // Jika video TIDAK ada TAPI prosesor masih aktif, bersihkan.
      console.log('🎵 Video tidak lagi terdeteksi. Membersihkan GUI.');
      audioProcessor.cleanup();
    }
  };

  // Gunakan MutationObserver untuk mendeteksi semua perubahan halaman (navigasi, dll.)
  const observer = new MutationObserver(() => {
    // Gunakan debounce untuk mencegah pemanggilan berlebihan saat halaman sedang sibuk loading
    clearTimeout(checkTimeout);
    checkTimeout = setTimeout(runCheck, 500);
  });

  // Mulai mengamati seluruh body halaman untuk perubahan apapun
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });

  // Jalankan pemeriksaan awal saat skrip dimuat
  setTimeout(runCheck, 1000);

})();